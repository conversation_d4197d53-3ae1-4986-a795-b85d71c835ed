import React, { useMemo, useCallback } from 'react';
import { useTranslation } from 'react-i18next';
import { Typography, Card, Table, Badge, Icon, ActionMenu, CollapsibleCard, Loading } from '@/shared/components/common';
import { CustomerDetailData } from './types';
import { TableColumn } from '@/shared/components/common/Table/types';
import MenuIconBar from '@/modules/components/menu-bar/MenuIconBar';
import { useDataTable, useDataTableConfig } from '@/shared/hooks/table';
import { SortDirection } from '@/shared/dto/request/query.dto';
import { useCustomerOrders } from '../../../hooks/useOrderQuery';
import { OrderQueryParams } from '../../../services/order.service';

interface CustomerOrdersProps {
  customer: CustomerDetailData;
}

// Interface cho dữ liệu order từ API customer orders
interface CustomerOrderItem {
  id: string;
  billInfo: {
    tax?: number;
    total: number;
    discount?: number;
    subtotal?: number;
    codAmount?: number;
    shippingFee?: number;
    paymentMethod?: string;
    paymentStatus?: string;
    status?: string;
    payment_method?: string;
  };
  shippingStatus: string;
  createdAt: string;
  source: string;
  orderStatus: string;
}

/**
 * Component hiển thị đơn hàng của khách hàng
 */
const CustomerOrders: React.FC<CustomerOrdersProps> = ({ customer }) => {
  const { t } = useTranslation('business');

  // Format currency
  const formatCurrency = useCallback((amount: number) => {
    return new Intl.NumberFormat('vi-VN', {
      style: 'currency',
      currency: 'VND',
    }).format(amount);
  }, []);

  // Format date
  const formatDate = useCallback((dateString: string) => {
    try {
      return new Date(dateString).toLocaleDateString('vi-VN', {
        day: '2-digit',
        month: '2-digit',
        year: 'numeric',
        hour: '2-digit',
        minute: '2-digit',
      });
    } catch {
      return dateString;
    }
  }, []);

  // Get status badge variant
  const getStatusVariant = useCallback((status: string) => {
    switch (status) {
      case 'delivered':
        return 'success';
      case 'shipped':
        return 'info';
      case 'processing':
        return 'warning';
      case 'pending':
        return 'info';
      case 'cancelled':
        return 'danger';
      default:
        return 'info';
    }
  }, []);

  // Get payment status variant
  const getPaymentStatusVariant = useCallback((status: string) => {
    switch (status) {
      case 'paid':
        return 'success';
      case 'pending':
        return 'warning';
      case 'failed':
        return 'danger';
      case 'refunded':
        return 'info';
      default:
        return 'info';
    }
  }, []);

  // Get status text
  const getStatusText = useCallback((status: string) => {
    return t(`customer.order.status.${status}`, status);
  }, [t]);

  // Get payment status text
  const getPaymentStatusText = useCallback((status: string) => {
    return t(`customer.order.paymentStatus.${status}`, status);
  }, [t]);

  // Table columns
  const columns: TableColumn<CustomerOrderItem>[] = useMemo(() => [
    {
      key: 'id',
      title: t('customer.detail.orderCode'),
      dataIndex: 'id',
      render: (value: unknown) => (
        <div className="flex items-center space-x-2">
          <Icon name="shopping-cart" size="sm" className="text-muted" />
          <Typography variant="body2" className="text-foreground font-medium">
            #{String(value)}
          </Typography>
        </div>
      ),
    },
    {
      key: 'createdAt',
      title: t('customer.detail.orderDate'),
      dataIndex: 'createdAt',
      render: (value: unknown) => (
        <Typography variant="body2" className="text-foreground">
          {formatDate(new Date(Number(value)).toISOString())}
        </Typography>
      ),
    },
    {
      key: 'orderStatus',
      title: t('customer.detail.orderStatus'),
      dataIndex: 'orderStatus',
      render: (value: unknown) => {
        const statusValue = String(value);
        return (
          <Badge variant={getStatusVariant(statusValue)} size="sm">
            {getStatusText(statusValue)}
          </Badge>
        );
      },
    },
    {
      key: 'billInfo',
      title: t('customer.detail.paymentStatus'),
      dataIndex: 'billInfo',
      render: (value: unknown) => {
        const billInfo = value as CustomerOrderItem['billInfo'];
        const paymentStatus = billInfo?.paymentStatus || billInfo?.status || 'unknown';
        return (
          <Badge variant={getPaymentStatusVariant(paymentStatus)} size="sm">
            {getPaymentStatusText(paymentStatus)}
          </Badge>
        );
      },
    },
    {
      key: 'billInfo',
      title: t('customer.detail.totalAmount'),
      dataIndex: 'billInfo',
      render: (value: unknown) => {
        const billInfo = value as CustomerOrderItem['billInfo'];
        return (
          <Typography variant="body2" className="text-foreground font-medium">
            {formatCurrency(billInfo?.total || 0)}
          </Typography>
        );
      },
    },
    {
      key: 'shippingStatus',
      title: t('customer.detail.shippingStatus'),
      dataIndex: 'shippingStatus',
      render: (value: unknown) => {
        const shippingStatus = String(value);
        return (
          <Badge variant={getStatusVariant(shippingStatus)} size="sm">
            {shippingStatus}
          </Badge>
        );
      },
    },
    {
      key: 'source',
      title: t('customer.detail.source'),
      dataIndex: 'source',
      render: (value: unknown) => (
        <Typography variant="body2" className="text-muted">
          {String(value)}
        </Typography>
      ),
    },
    {
      key: 'actions',
      title: t('common.actions'),
      render: (_, record: CustomerOrderItem) => (
        <ActionMenu
          items={[
            {
              id: 'view',
              label: t('common.view'),
              icon: 'eye',
              onClick: () => handleViewOrder(record.id),
            },
            {
              id: 'edit',
              label: t('common.edit'),
              icon: 'edit',
              onClick: () => handleEditOrder(record.id),
            },
            {
              id: 'divider1',
              divider: true,
            },
            {
              id: 'cancel',
              label: t('customer.order.cancel'),
              icon: 'x',
              onClick: () => handleCancelOrder(record.id),
              disabled: record.orderStatus === 'completed' || record.orderStatus === 'cancelled',
            },
          ]}
        />
      ),
    },
  ], [t, formatCurrency, formatDate, getStatusVariant, getStatusText, getPaymentStatusVariant, getPaymentStatusText]);

  // Action handlers
  const handleViewOrder = (orderId: string) => {
    console.log('View order:', orderId);
    // Implement view order logic
  };

  const handleEditOrder = (orderId: string) => {
    console.log('Edit order:', orderId);
    // Implement edit order logic
  };

  const handleCancelOrder = (orderId: string) => {
    console.log('Cancel order:', orderId);
    // Implement cancel order logic
  };

  // Create query params function
  const createQueryParams = (params: {
    page: number;
    pageSize: number;
    searchTerm: string;
    sortBy: string | null;
    sortDirection: SortDirection | null;
    filterValue: string | number | boolean | undefined;
  }): OrderQueryParams => ({
    page: params.page,
    limit: params.pageSize,
    search: params.searchTerm || undefined,
    sortBy: params.sortBy || undefined,
    sortDirection: params.sortDirection || undefined,
    status: params.filterValue && params.filterValue !== 'all' ? String(params.filterValue) : undefined,
  });

  // Filter options for order status
  const filterOptions = [
    { id: 'all', label: t('common.all'), icon: 'list', value: 'all' },
    { id: 'pending', label: t('customer.order.status.pending', 'Pending'), icon: 'clock', value: 'pending' },
    { id: 'processing', label: t('customer.order.status.processing', 'Processing'), icon: 'loader', value: 'processing' },
    { id: 'shipped', label: t('customer.order.status.shipped', 'Shipped'), icon: 'truck', value: 'shipped' },
    { id: 'delivered', label: t('customer.order.status.delivered', 'Delivered'), icon: 'check-circle', value: 'delivered' },
    { id: 'cancelled', label: t('customer.order.status.cancelled', 'Cancelled'), icon: 'x-circle', value: 'cancelled' },
  ];

  // Use data table hook
  const dataTable = useDataTable(
    useDataTableConfig<CustomerOrderItem, OrderQueryParams>({
      columns,
      filterOptions,
      createQueryParams,
    })
  );

  // Gọi API lấy danh sách đơn hàng của khách hàng
  const { data: ordersResponse, isLoading } = useCustomerOrders(
    customer.id,
    dataTable.queryParams
  );

  const ordersData = ordersResponse || {
    items: [],
    meta: {
      currentPage: 1,
      totalItems: 0,
      totalPages: 1,
    },
  };

  return (
    <CollapsibleCard
      title={
        <div className="flex items-center justify-between w-full">
          <Typography variant="h6" className="text-foreground">
            {t('customer.detail.orders')}
          </Typography>
          <Typography variant="body2" className="text-muted">
            {isLoading ? (
              <Loading size="sm" />
            ) : (
              `${ordersData.meta.totalItems} ${t('customer.detail.totalOrders').toLowerCase()}`
            )}
          </Typography>
        </div>
      }
      defaultOpen={false}
    >
      <div className="space-y-4">
        {/* Menu Icon Bar */}
        <MenuIconBar
          onSearch={dataTable.tableData.handleSearch}
          items={dataTable.menuItems}
          onColumnVisibilityChange={dataTable.columnVisibility.setVisibleColumns}
          columns={dataTable.columnVisibility.visibleColumns}
          showDateFilter={false}
          showColumnFilter={true}
        />

        {/* Table */}
        <Card className="overflow-hidden">
          <Table<CustomerOrderItem>
            columns={dataTable.columnVisibility.visibleTableColumns}
            data={ordersData.items}
            rowKey="id"
            loading={isLoading}
            sortable={true}
            onSortChange={dataTable.tableData.handleSortChange}
            pagination={{
              current: ordersData.meta.currentPage,
              pageSize: dataTable.tableData.pageSize,
              total: ordersData.meta.totalItems,
              onChange: dataTable.tableData.handlePageChange,
              showSizeChanger: true,
              pageSizeOptions: [10, 20, 50, 100],
              showFirstLastButtons: true,
              showPageInfo: true,
            }}
          />
        </Card>
      </div>
    </CollapsibleCard>
  );
};

export default CustomerOrders;
